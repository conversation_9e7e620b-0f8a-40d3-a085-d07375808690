name: 'Setup Playwright with Enhanced Stability'
description: 'Installs and verifies Playwright browsers with comprehensive error handling'
inputs:
  browsers:
    description: 'Browsers to install (space-separated)'
    required: false
    default: 'webkit chromium'
  skip-install:
    description: 'Skip browser installation (use existing cache)'
    required: false
    default: 'false'

runs:
  using: 'composite'
  steps:
    - name: Check system resources before Playwright install
      shell: bash
      run: |
        echo "=== Pre-install System Check ==="
        echo "Disk usage:"
        df -h
        echo ""
        echo "Available memory:"
        if [[ "$RUNNER_OS" == "macOS" ]]; then
          vm_stat | head -5
        else
          free -h
        fi
        echo ""
        echo "Playwright cache permissions:"
        ls -ld ~/.cache/ms-playwright || echo "Cache directory does not exist yet"
        echo ""
        echo "Temp directory permissions:"
        ls -ld /tmp || true

    - name: Check disk space requirements
      shell: bash
      run: |
        # Check if we have at least 2GB free space
        AVAILABLE_KB=$(df / | tail -1 | awk '{print $4}')
        REQUIRED_KB=2097152  # 2GB in KB
        
        if [ "$AVAILABLE_KB" -lt "$REQUIRED_KB" ]; then
          echo "❌ Insufficient disk space: ${AVAILABLE_KB}KB available, ${REQUIRED_KB}KB required"
          echo "Available space: $(($AVAILABLE_KB / 1024))MB"
          echo "Required space: $(($REQUIRED_KB / 1024))MB"
          exit 1
        else
          echo "✅ Sufficient disk space: $(($AVAILABLE_KB / 1024))MB available"
        fi

    - name: Ensure PLAYWRIGHT_BROWSERS_PATH is unset
      shell: bash
      run: |
        echo "Ensuring PLAYWRIGHT_BROWSERS_PATH is unset..."
        unset PLAYWRIGHT_BROWSERS_PATH
        echo "PLAYWRIGHT_BROWSERS_PATH status: ${PLAYWRIGHT_BROWSERS_PATH:-'(unset)'}"

    - name: Clean and install Playwright browsers with retry
      if: inputs.skip-install != 'true'
      shell: bash
      run: |
        echo "Installing Playwright browsers: ${{ inputs.browsers }}"
        unset PLAYWRIGHT_BROWSERS_PATH
        
        # Clean existing installations
        npx playwright uninstall --all || true
        rm -rf ~/.cache/ms-playwright || true
        rm -rf /tmp/playwright-* || true
        mkdir -p ~/.cache/ms-playwright || true
        
        # Install with retry logic
        for i in {1..3}; do
          echo "Installation attempt $i/3"
          if npx playwright install --with-deps ${{ inputs.browsers }}; then
            echo "✅ Browser installation successful on attempt $i"
            break
          else
            echo "❌ Installation attempt $i failed"
            if [ $i -lt 3 ]; then
              echo "Cleaning up before retry..."
              rm -rf ~/.cache/ms-playwright || true
              sleep 5
            else
              echo "All installation attempts failed"
              exit 1
            fi
          fi
        done

    - name: Verify browser binaries exist
      shell: bash
      run: |
        echo "Verifying Playwright browser binaries..."
        if [ ! -d ~/.cache/ms-playwright ]; then
          echo "❌ Playwright cache folder missing"
          exit 1
        fi
        
        echo "Checking for browser executables..."
        BROWSER_COUNT=$(find ~/.cache/ms-playwright -type f -executable | grep -E 'webkit|chromium' | wc -l)
        if [ "$BROWSER_COUNT" -gt 0 ]; then
          echo "✅ Found $BROWSER_COUNT browser executables"
          find ~/.cache/ms-playwright -type f -executable | grep -E 'webkit|chromium' | head -5
        else
          echo "❌ No browser executables found"
          echo "Cache directory contents:"
          ls -la ~/.cache/ms-playwright/ || true
          exit 1
        fi

    - name: Verify installation with dry-run
      shell: bash
      run: |
        echo "Verifying browser installation with dry-run check..."
        unset PLAYWRIGHT_BROWSERS_PATH
        
        if npx playwright install --dry-run ${{ inputs.browsers }} 2>&1 | grep -q "is already installed"; then
          echo "✅ Browsers are properly installed (dry-run verification)"
        else
          echo "❌ Browser installation verification failed (dry-run check)"
          echo "Dry-run output:"
          npx playwright install --dry-run ${{ inputs.browsers }} 2>&1 || true
          exit 1
        fi

    - name: Fallback reinstall on failure
      if: failure()
      shell: bash
      run: |
        echo "=== FALLBACK: Force clean reinstall ==="
        echo "Removing node_modules and reinstalling..."
        rm -rf node_modules/
        npm ci
        
        echo "Force cleaning all Playwright data..."
        rm -rf ~/.cache/ms-playwright
        rm -rf /tmp/playwright-*
        
        echo "Reinstalling Playwright browsers..."
        npx playwright install --with-deps ${{ inputs.browsers }}
        
        echo "Final verification..."
        npx playwright --version
        find ~/.cache/ms-playwright -type f -executable | grep -E 'webkit|chromium' | head -5
